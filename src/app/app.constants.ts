import localFont from "next/font/local";

export enum FuzzerType {
  MEDUSA = "MEDUSA",
  ECHIDNA = "ECHIDNA",
  FOUNDRY = "FOUNDRY",
  HALMOS = "HALMOS",
  KONTROL = "KONTROL",
}

export const blenderPro = localFont({
  src: [
    {
      path: "./fonts/BlenderPro-Heavy.woff2",
      weight: "900",
      style: "normal",
    },
    {
      path: "./fonts/BlenderPro-Bold.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "./fonts/BlenderPro-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "./fonts/BlenderPro-Thin.woff2",
      weight: "100",
      style: "normal",
    },
  ],
  variable: "--font-blender-pro",
});
