"use client";

import { cn } from "../helpers/cn";
import { FuzzerType } from "../app.constants";
import { AppButton } from "./app-button";
import { H3, H5, H6 } from "./app-typography";

type JobTypeFuzzerProps = {
  value: FuzzerType;
  onChange: (value: FuzzerType) => void;
  className?: string;
};

const jobTypeOptions = [
  { label: "Medusa", value: FuzzerType.MEDUSA },
  { label: "Echidna", value: FuzzerType.ECHIDNA },
  { label: "Foundry", value: FuzzerType.FOUNDRY },
  { label: "Halmos", value: FuzzerType.HALMOS },
  { label: "Kontrol", value: FuzzerType.KONTROL },
];

export const JobTypeFuzzer = ({
  value,
  onChange,
  className,
}: JobTypeFuzzerProps) => {
  return (
    <div
      className={cn(
        "flex flex-col gap-3 pb-4 border-b border-back-neutral-tertiary",
        className
      )}
    >
      <div className="flex items-center gap-4">
        <H6>Select Job Type:</H6>

        <div className="flex flex-row flex-wrap gap-3">
          {jobTypeOptions.map((option) => {
            const isSelected = value === option.value;

            return (
              <AppButton
                key={option.value}
                onClick={() => onChange(option.value)}
                variant={isSelected ? "primary" : "outline"}
              >
                {option.label}
              </AppButton>
            );
          })}
        </div>
      </div>
    </div>
  );
};
