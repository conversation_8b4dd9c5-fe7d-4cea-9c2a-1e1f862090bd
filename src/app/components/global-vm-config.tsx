"use client";

import { useEffect, useState } from "react";
import { FiRefreshCw, FiSettings } from "react-icons/fi";

import { FuzzerType } from "@/app/app.constants";
import { AppButton } from "@/app/components/app-button";
import { Body3, Body4, H5 } from "@/app/components/app-typography";
import type { GlobalVmConfig } from "@/app/utils/vm-config";
import {
  getCurrentVmConfig,
  getToolDefaults,
  isUsingToolDefaults,
  resetToToolDefaults,
  updateVmConfig,
} from "@/app/utils/vm-config";

interface GlobalVmConfigProps {
  onConfigChange?: (tool: FuzzerType, config: GlobalVmConfig) => void;
  className?: string;
}

export function GlobalVmConfigComponent({
  onConfigChange,
  className = "",
}: GlobalVmConfigProps) {
  const tools = [FuzzerType.MEDUSA, FuzzerType.ECHIDNA, FuzzerType.HALMOS];

  const [configs, setConfigs] = useState<
    Partial<Record<FuzzerType, GlobalVmConfig>>
  >({
    [FuzzerType.MEDUSA]: { prank: false, roll: false, time: false },
    [FuzzerType.ECHIDNA]: { prank: false, roll: false, time: false },
    [FuzzerType.HALMOS]: { prank: false, roll: false, time: false },
  });

  // Load configurations on mount
  useEffect(() => {
    const toolsToLoad = [
      FuzzerType.MEDUSA,
      FuzzerType.ECHIDNA,
      FuzzerType.HALMOS,
    ];
    const newConfigs: Record<FuzzerType, GlobalVmConfig> = {} as Record<
      FuzzerType,
      GlobalVmConfig
    >;
    toolsToLoad.forEach((tool) => {
      const currentConfig = getCurrentVmConfig(tool);
      newConfigs[tool] = currentConfig;
    });
    setConfigs(newConfigs);
  }, []);

  const handleToggle = (tool: FuzzerType, property: keyof GlobalVmConfig) => {
    const currentConfig = configs[tool];
    if (!currentConfig) return;

    const newConfig = updateVmConfig(tool, currentConfig, {
      [property]: !currentConfig[property],
    });

    setConfigs((prev) => ({
      ...prev,
      [tool]: newConfig,
    }));

    if (onConfigChange) {
      onConfigChange(tool, newConfig);
    }
  };

  // Listen for external config changes
  useEffect(() => {
    const toolsToUpdate = [
      FuzzerType.MEDUSA,
      FuzzerType.ECHIDNA,
      FuzzerType.HALMOS,
    ];

    const handleConfigChange = (event: CustomEvent) => {
      const { tool, config } = event.detail as {
        tool: FuzzerType;
        config: GlobalVmConfig;
      };
      // Only update the specific tool that changed
      if (toolsToUpdate.includes(tool)) {
        setConfigs((prev) => ({
          ...prev,
          [tool]: config,
        }));
      }
    };

    const handleStorageChange = () => {
      const newConfigs: Record<FuzzerType, GlobalVmConfig> = {} as Record<
        FuzzerType,
        GlobalVmConfig
      >;
      toolsToUpdate.forEach((tool) => {
        const currentConfig = getCurrentVmConfig(tool);
        newConfigs[tool] = currentConfig;
      });
      setConfigs(newConfigs);
    };

    window.addEventListener(
      "vm-config-changed",
      handleConfigChange as EventListener
    );
    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener(
        "vm-config-changed",
        handleConfigChange as EventListener
      );
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  const handleResetToDefaults = (tool: FuzzerType) => {
    const defaults = resetToToolDefaults(tool);
    setConfigs((prev) => ({
      ...prev,
      [tool]: defaults,
    }));

    if (onConfigChange) {
      onConfigChange(tool, defaults);
    }
  };

  const getToolDisplayName = (tool: FuzzerType): string => {
    switch (tool) {
      case "ECHIDNA":
        return "Echidna";
      case "MEDUSA":
        return "Medusa";
      case "HALMOS":
        return "Halmos";
      case "FOUNDRY":
        return "Foundry";
      case "KONTROL":
        return "Kontrol";
      default:
        return tool;
    }
  };

  return (
    <div
      className={`mb-2 rounded-lg border border-stroke-neutral-decorative bg-back-neutral-secondary p-4 ${className}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FiSettings className="size-4 text-fore-neutral-secondary" />
          <H5 className="text-fore-neutral-primary">VM Configuration</H5>
        </div>
      </div>

      <div className="mt-4 space-y-6">
        <Body3 className="text-fore-neutral-secondary">
          Global VM configuration applies to all reproduction code generation.
          These settings persist across browser sessions.
        </Body3>

        {tools.map((tool) => {
          const config = configs[tool];
          const toolDefaults = getToolDefaults(tool);
          const isUsingDefaults = config
            ? isUsingToolDefaults(config, tool)
            : true;

          if (!config) return null;

          return (
            <div key={tool} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <H5 className="text-fore-neutral-primary">
                    {getToolDisplayName(tool)}
                  </H5>
                  {isUsingDefaults && (
                    <Body4 className="text-fore-neutral-secondary">
                      (Using defaults)
                    </Body4>
                  )}
                </div>
                <AppButton
                  variant="outline"
                  size="sm"
                  onClick={() => handleResetToDefaults(tool)}
                  leftIcon={<FiRefreshCw className="size-3" />}
                >
                  Reset
                </AppButton>
              </div>

              <div className="grid grid-cols-1 gap-3 sm:grid-cols-3">
                <VmToggleButton
                  label="vm.prank"
                  description="Use vm.prank for msg.sender manipulation"
                  isEnabled={config.prank}
                  isDefault={toolDefaults.prank}
                  onClick={() => handleToggle(tool, "prank")}
                />
                <VmToggleButton
                  label="vm.roll"
                  description="Use vm.roll for block.number manipulation"
                  isEnabled={config.roll}
                  isDefault={toolDefaults.roll}
                  onClick={() => handleToggle(tool, "roll")}
                />
                <VmToggleButton
                  label="vm.warp"
                  description="Use vm.warp for block.timestamp manipulation"
                  isEnabled={config.time}
                  isDefault={toolDefaults.time}
                  onClick={() => handleToggle(tool, "time")}
                />
              </div>
            </div>
          );
        })}

        <div className="mt-4 rounded-md bg-back-neutral-tertiary p-3">
          <Body4 className="text-fore-neutral-secondary">
            <strong>Tool Defaults:</strong>
            <br />
            • Echidna: All VM options enabled (comprehensive reproduction)
            <br />• Medusa/Halmos: All VM options disabled (minimal
            reproduction)
          </Body4>
        </div>
      </div>
    </div>
  );
}

interface VmToggleButtonProps {
  label: string;
  description: string;
  isEnabled: boolean;
  isDefault: boolean;
  onClick: () => void;
}

function VmToggleButton({
  label,
  description,
  isEnabled,
  isDefault,
  onClick,
}: VmToggleButtonProps) {
  return (
    <div className="rounded-md border border-stroke-neutral-decorative bg-back-neutral-primary p-3">
      <div className="mb-2 flex items-center justify-between">
        <Body3 className="font-medium text-fore-neutral-primary">{label}</Body3>
        <div className="flex items-center gap-2">
          {isDefault && (
            <Body4 className="text-xs text-fore-neutral-secondary">
              default
            </Body4>
          )}
          <AppButton
            variant={isEnabled ? "primary" : "outline"}
            size="xs"
            onClick={onClick}
          >
            {isEnabled ? "ON" : "OFF"}
          </AppButton>
        </div>
      </div>
      <Body4 className="text-xs text-fore-neutral-secondary">
        {description}
      </Body4>
    </div>
  );
}
