import { FuzzerType } from "@/app/app.constants";

export interface GlobalVmConfig {
  prank: boolean;
  roll: boolean;
  time: boolean;
}

export interface VmConfigWithDefaults extends GlobalVmConfig {
  isUsingDefaults: boolean;
  currentTool: FuzzerType | null;
}

const VM_CONFIG_STORAGE_KEY_PREFIX = "recon-vm-config";

const TOOL_DEFAULTS: Record<FuzzerType, GlobalVmConfig> = {
  [FuzzerType.ECHIDNA]: {
    prank: true,
    roll: true,
    time: true,
  },
  [FuzzerType.MEDUSA]: {
    prank: false,
    roll: false,
    time: false,
  },
  [FuzzerType.HALMOS]: {
    prank: false,
    roll: false,
    time: false,
  },
  [FuzzerType.FOUNDRY]: {
    prank: false,
    roll: false,
    time: false,
  },
  [FuzzerType.KONTROL]: {
    prank: false,
    roll: false,
    time: false,
  },
};

function getStorageKey(tool: FuzzerType): string {
  return `${VM_CONFIG_STORAGE_KEY_PREFIX}-${tool.toLowerCase()}`;
}

export function getToolDefaults(tool: FuzzerType): GlobalVmConfig {
  return TOOL_DEFAULTS[tool];
}

export function loadVmConfigFromStorage(
  tool: FuzzerType
): GlobalVmConfig | null {
  if (typeof window === "undefined") return null;

  try {
    const stored = localStorage.getItem(getStorageKey(tool));
    if (!stored) return null;

    const parsed = JSON.parse(stored);

    // Validate the structure
    if (
      typeof parsed === "object" &&
      typeof parsed.prank === "boolean" &&
      typeof parsed.roll === "boolean" &&
      typeof parsed.time === "boolean"
    ) {
      return parsed;
    }

    return null;
  } catch (error) {
    console.warn("Failed to load VM config from localStorage:", error);
    return null;
  }
}

export function saveVmConfigToStorage(
  tool: FuzzerType,
  config: GlobalVmConfig
): void {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(getStorageKey(tool), JSON.stringify(config));
    window.dispatchEvent(
      new CustomEvent("vm-config-changed", { detail: { tool, config } })
    );
  } catch (error) {
    console.warn("Failed to save VM config to localStorage:", error);
  }
}

export function getCurrentVmConfig(tool: FuzzerType): VmConfigWithDefaults {
  const storedConfig = loadVmConfigFromStorage(tool);

  if (storedConfig) {
    return {
      ...storedConfig,
      isUsingDefaults: false,
      currentTool: tool,
    };
  }

  const defaults = getToolDefaults(tool);
  return {
    ...defaults,
    isUsingDefaults: true,
    currentTool: tool,
  };
}

export function resetToToolDefaults(tool: FuzzerType): GlobalVmConfig {
  const defaults = getToolDefaults(tool);
  saveVmConfigToStorage(tool, defaults);
  return defaults;
}

export function updateVmConfig(
  tool: FuzzerType,
  currentConfig: GlobalVmConfig,
  updates: Partial<GlobalVmConfig>
): GlobalVmConfig {
  const newConfig = {
    ...currentConfig,
    ...updates,
  };

  saveVmConfigToStorage(tool, newConfig);
  return newConfig;
}

export function isUsingToolDefaults(
  config: GlobalVmConfig,
  tool: FuzzerType
): boolean {
  const defaults = getToolDefaults(tool);
  return (
    config.prank === defaults.prank &&
    config.roll === defaults.roll &&
    config.time === defaults.time
  );
}
